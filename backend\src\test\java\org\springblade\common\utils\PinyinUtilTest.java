package org.springblade.common.utils;

import org.junit.jupiter.api.Test;

/**
 * 拼音工具类测试
 */
public class PinyinUtilTest {

    @Test
    public void testGenerateBaseUsername() {
        // 测试正常中文姓名
        System.out.println("张三: " + PinyinUtil.generateBaseUsername("张三"));
        System.out.println("李小明: " + PinyinUtil.generateBaseUsername("李小明"));
        System.out.println("王: " + PinyinUtil.generateBaseUsername("王"));
        
        // 测试英文姓名
        System.out.println("<PERSON>: " + PinyinUtil.generateBaseUsername("<PERSON>"));
        System.out.println("A: " + PinyinUtil.generateBaseUsername("A"));
        
        // 测试特殊情况
        System.out.println("空字符串: " + PinyinUtil.generateBaseUsername(""));
        System.out.println("null: " + PinyinUtil.generateBaseUsername(null));
        System.out.println("纯符号 @#$: " + PinyinUtil.generateBaseUsername("@#$"));
        System.out.println("数字 123: " + PinyinUtil.generateBaseUsername("123"));
        System.out.println("中英混合 张John: " + PinyinUtil.generateBaseUsername("张John"));
        
        // 测试拼音首字母
        System.out.println("张三 首字母: " + PinyinUtil.getFirstLetters("张三"));
        System.out.println("李小明 首字母: " + PinyinUtil.getFirstLetters("李小明"));
        System.out.println("王 首字母: " + PinyinUtil.getFirstLetters("王"));
        System.out.println("@#$ 首字母: " + PinyinUtil.getFirstLetters("@#$"));
        
        // 测试完整拼音
        System.out.println("张三 完整拼音: " + PinyinUtil.getFullPinyin("张三"));
        System.out.println("李小明 完整拼音: " + PinyinUtil.getFullPinyin("李小明"));
        System.out.println("王 完整拼音: " + PinyinUtil.getFullPinyin("王"));
        System.out.println("@#$ 完整拼音: " + PinyinUtil.getFullPinyin("@#$"));
    }
}
