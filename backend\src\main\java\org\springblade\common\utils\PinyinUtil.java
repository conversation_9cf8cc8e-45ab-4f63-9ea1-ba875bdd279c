/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chi<PERSON> (<EMAIL>)
 */
package org.springblade.common.utils;

import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;

/**
 * 拼音转换工具类
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
public class PinyinUtil {

	/**
	 * 获取汉字的拼音首字母
	 *
	 * @param chinese 汉字字符串
	 * @return 拼音首字母（小写）
	 */
	public static String getFirstLetters(String chinese) {
		if (chinese == null || chinese.trim().isEmpty()) {
			return "";
		}

		StringBuilder result = new StringBuilder();
		HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
		format.setCaseType(HanyuPinyinCaseType.LOWERCASE);
		format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
		format.setVCharType(HanyuPinyinVCharType.WITH_V);

		char[] chars = chinese.toCharArray();
		for (char ch : chars) {
			if (Character.toString(ch).matches("[\\u4E00-\\u9FA5]+")) {
				// 是汉字
				try {
					String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(ch, format);
					if (pinyinArray != null && pinyinArray.length > 0) {
						result.append(pinyinArray[0].charAt(0));
					}
				} catch (BadHanyuPinyinOutputFormatCombination e) {
					// 转换失败，跳过该字符
				}
			} else if (Character.isLetter(ch)) {
				// 是英文字母
				result.append(Character.toLowerCase(ch));
			}
			// 其他字符（数字、符号等）忽略
		}

		return result.toString();
	}

	/**
	 * 获取汉字的完整拼音
	 *
	 * @param chinese 汉字字符串
	 * @return 完整拼音（小写，无音调）
	 */
	public static String getFullPinyin(String chinese) {
		if (chinese == null || chinese.trim().isEmpty()) {
			return "";
		}

		StringBuilder result = new StringBuilder();
		HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
		format.setCaseType(HanyuPinyinCaseType.LOWERCASE);
		format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
		format.setVCharType(HanyuPinyinVCharType.WITH_V);

		char[] chars = chinese.toCharArray();
		for (char ch : chars) {
			if (Character.toString(ch).matches("[\\u4E00-\\u9FA5]+")) {
				// 是汉字
				try {
					String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(ch, format);
					if (pinyinArray != null && pinyinArray.length > 0) {
						result.append(pinyinArray[0]);
					}
				} catch (BadHanyuPinyinOutputFormatCombination e) {
					// 转换失败，跳过该字符
				}
			} else if (Character.isLetter(ch)) {
				// 是英文字母
				result.append(Character.toLowerCase(ch));
			}
			// 其他字符（数字、符号等）忽略
		}

		return result.toString();
	}

	/**
	 * 生成用户账号（拼音首字母 + 数字后缀，确保唯一性）
	 *
	 * @param realName 真实姓名
	 * @return 用户账号
	 */
	public static String generateUsername(String realName) {
		if (realName == null || realName.trim().isEmpty()) {
			return "user" + System.currentTimeMillis() % 10000;
		}

		String firstLetters = getFirstLetters(realName);
		if (firstLetters.isEmpty()) {
			return "user" + System.currentTimeMillis() % 10000;
		}

		// 如果拼音首字母太短，使用完整拼音的前6位
		if (firstLetters.length() < 2) {
			String fullPinyin = getFullPinyin(realName);
			if (fullPinyin.length() > 6) {
				firstLetters = fullPinyin.substring(0, 6);
			} else {
				firstLetters = fullPinyin;
			}
		}

		// 添加时间戳后缀确保唯一性
		String timestamp = String.valueOf(System.currentTimeMillis() % 10000);
		return firstLetters + timestamp;
	}

}
