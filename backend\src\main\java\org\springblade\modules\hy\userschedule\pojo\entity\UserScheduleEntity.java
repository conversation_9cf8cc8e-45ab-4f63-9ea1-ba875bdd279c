/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.hy.userschedule.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;
import java.io.Serial;

/**
 * 用户日程信息表 实体类
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@TableName("hy_user_schedule")
@Schema(description = "UserSchedule对象")
@EqualsAndHashCode(callSuper = true)
public class UserScheduleEntity extends BaseEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 用户ID
	 */
	@Schema(description = "用户ID")
	private Long userId;

	/**
	 * 日程信息（富文本）
	 */
	@Schema(description = "日程信息（富文本）")
	private String scheduleContent;

	/**
	 * 用餐信息（JSON格式）
	 */
	@Schema(description = "用餐信息（JSON格式）")
	private String diningInfo;

	/**
	 * 住宿信息（JSON格式）
	 */
	@Schema(description = "住宿信息（JSON格式）")
	private String accommodationInfo;

	/**
	 * 用户真实姓名（查询用）
	 */
	@Schema(description = "用户真实姓名（查询用）")
	private transient String userRealName;

	/**
	 * 用户手机号（编辑用）
	 */
	@Schema(description = "用户手机号（编辑用）")
	private transient String userPhone;

	/**
	 * 用户工号（编辑用）
	 */
	@Schema(description = "用户工号（编辑用）")
	private transient String userEmployeeNumber;

	/**
	 * 用户房号（编辑用）
	 */
	@Schema(description = "用户房号（编辑用）")
	private transient String userRoomNumber;

	/**
	 * 用户会议座位号（编辑用）
	 */
	@Schema(description = "用户会议座位号（编辑用）")
	private transient String userMeetingSeatNumber;

}
