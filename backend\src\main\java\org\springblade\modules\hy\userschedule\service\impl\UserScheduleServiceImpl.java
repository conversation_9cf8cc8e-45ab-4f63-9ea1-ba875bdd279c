/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.hy.userschedule.service.impl;

import org.springblade.modules.hy.userschedule.pojo.entity.UserScheduleEntity;
import org.springblade.modules.hy.userschedule.pojo.vo.UserScheduleVO;
import org.springblade.modules.hy.userschedule.pojo.excel.UserScheduleExcel;
import org.springblade.modules.hy.userschedule.pojo.excel.UserScheduleImportExcel;
import org.springblade.modules.hy.userschedule.pojo.dto.UserScheduleImportResultDTO;
import org.springblade.modules.system.pojo.entity.User;
import org.springblade.modules.system.service.IUserService;
import org.springblade.common.utils.PinyinUtil;
import org.springblade.core.tool.utils.DigestUtil;
import org.springblade.common.constant.CommonConstant;
import com.alibaba.excel.EasyExcel;
import org.springblade.core.tool.utils.StringUtil;
import org.springframework.web.multipart.MultipartFile;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.ArrayList;
import lombok.AllArgsConstructor;
import org.springblade.modules.hy.userschedule.mapper.UserScheduleMapper;
import org.springblade.modules.hy.userschedule.service.IUserScheduleService;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;

/**
 * 用户日程信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Service
@AllArgsConstructor
public class UserScheduleServiceImpl extends BaseServiceImpl<UserScheduleMapper, UserScheduleEntity> implements IUserScheduleService {

	private final IUserService userService;

	@Override
	public IPage<UserScheduleVO> selectUserSchedulePage(IPage<UserScheduleVO> page, UserScheduleVO userSchedule) {
		return page.setRecords(baseMapper.selectUserSchedulePage(page, userSchedule));
	}

	@Override
	public List<UserScheduleExcel> exportUserSchedule(Wrapper<UserScheduleEntity> queryWrapper) {
		return baseMapper.exportUserSchedule(queryWrapper);
	}

	@Override
	public UserScheduleVO getByUserId(Long userId) {
		return baseMapper.selectByUserId(userId);
	}

	@Override
	public boolean saveOrUpdateByUserId(UserScheduleEntity userSchedule) {
		// 根据用户ID查询是否已存在记录
		LambdaQueryWrapper<UserScheduleEntity> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(UserScheduleEntity::getUserId, userSchedule.getUserId());
		queryWrapper.eq(UserScheduleEntity::getIsDeleted, 0);

		UserScheduleEntity existingSchedule = this.getOne(queryWrapper);

		if (existingSchedule != null) {
			// 更新现有记录
			userSchedule.setId(existingSchedule.getId());
			return this.updateById(userSchedule);
		} else {
			// 创建新记录
			return this.save(userSchedule);
		}
	}

	@Override
	public UserScheduleImportResultDTO importUserSchedule(MultipartFile file) {
		UserScheduleImportResultDTO result = new UserScheduleImportResultDTO();
		List<String> errorMessages = new ArrayList<>();

		try {
			// 读取Excel文件
			List<UserScheduleImportExcel> importList = EasyExcel.read(file.getInputStream())
				.head(UserScheduleImportExcel.class)
				.sheet()
				.doReadSync();

			if (importList == null || importList.isEmpty()) {
				result.setSuccess(false);
				result.setMessage("Excel文件为空或格式不正确");
				return result;
			}

			int totalCount = importList.size();
			int successCount = 0;
			int failCount = 0;
			int updateCount = 0;
			int insertCount = 0;

			// 逐行处理数据
			for (int i = 0; i < importList.size(); i++) {
				UserScheduleImportExcel importData = importList.get(i);
				int rowNum = i + 2; // Excel行号从2开始（第1行是标题）

				try {
					// 数据验证
					if (StringUtil.isBlank(importData.getUserRealName())) {
						errorMessages.add("第" + rowNum + "行：用户姓名不能为空");
						failCount++;
						continue;
					}

					if (StringUtil.isBlank(importData.getScheduleContent())) {
						errorMessages.add("第" + rowNum + "行：日程信息不能为空");
						failCount++;
						continue;
					}

					// 根据用户姓名查找或创建用户
					User user = findOrCreateUser(importData, rowNum, errorMessages);
					if (user == null) {
						failCount++;
						continue;
					}

					// 创建或更新用户日程
					UserScheduleEntity scheduleEntity = new UserScheduleEntity();
					scheduleEntity.setUserId(user.getId());
					scheduleEntity.setScheduleContent(importData.getScheduleContent());
					scheduleEntity.setDiningInfo(importData.getDiningInfo());
					scheduleEntity.setAccommodationInfo(importData.getAccommodationInfo());
					scheduleEntity.setUserRealName(importData.getUserRealName());

					// 检查是否已存在该用户的日程
					LambdaQueryWrapper<UserScheduleEntity> queryWrapper = new LambdaQueryWrapper<>();
					queryWrapper.eq(UserScheduleEntity::getUserId, user.getId());
					queryWrapper.eq(UserScheduleEntity::getIsDeleted, 0);
					UserScheduleEntity existingSchedule = this.getOne(queryWrapper);

					if (existingSchedule != null) {
						// 更新现有记录
						scheduleEntity.setId(existingSchedule.getId());
						this.updateById(scheduleEntity);
						updateCount++;
					} else {
						// 创建新记录
						this.save(scheduleEntity);
						insertCount++;
					}

					successCount++;

				} catch (Exception e) {
					errorMessages.add("第" + rowNum + "行：" + e.getMessage());
					failCount++;
				}
			}

			// 设置结果
			result.setSuccess(failCount == 0);
			result.setTotalCount(totalCount);
			result.setSuccessCount(successCount);
			result.setFailCount(failCount);
			result.setUpdateCount(updateCount);
			result.setInsertCount(insertCount);
			result.setErrorMessages(errorMessages);

			if (failCount == 0) {
				result.setMessage("导入成功！共处理" + successCount + "条数据（新增" + insertCount + "条，更新" + updateCount + "条）");
			} else {
				result.setMessage("导入完成！成功" + successCount + "条，失败" + failCount + "条");
			}

		} catch (Exception e) {
			result.setSuccess(false);
			result.setMessage("导入失败：" + e.getMessage());
			errorMessages.add("文件解析失败：" + e.getMessage());
			result.setErrorMessages(errorMessages);
		}

		return result;
	}

	/**
	 * 根据导入数据查找或创建用户
	 */
	private User findOrCreateUser(UserScheduleImportExcel importData, int rowNum, List<String> errorMessages) {
		try {
			// 先根据姓名查找用户
			LambdaQueryWrapper<User> userQuery = new LambdaQueryWrapper<>();
			userQuery.eq(User::getRealName, importData.getUserRealName());
			User user = userService.getOne(userQuery);

			boolean userUpdated = false;

			if (user == null) {
				// 用户不存在，创建新用户
				user = new User();
				user.setRealName(importData.getUserRealName());
				user.setName(importData.getUserRealName()); // 昵称默认为真实姓名
				user.setTenantId("000000");
				user.setUserType(1);

				// 生成账号（使用拼音码）
				String username = generateUniqueUsername(importData.getUserRealName());
				user.setAccount(username);

				// 设置默认密码
				user.setPassword(DigestUtil.encrypt(CommonConstant.DEFAULT_PASSWORD));

				// 设置其他用户信息
				if (StringUtil.isNotBlank(importData.getUserPhone())) {
					user.setPhone(importData.getUserPhone());
				}
				if (StringUtil.isNotBlank(importData.getUserEmployeeNumber())) {
					user.setEmployeeNumber(importData.getUserEmployeeNumber());
				}
				if (StringUtil.isNotBlank(importData.getUserRoomNumber())) {
					user.setRoomNumber(importData.getUserRoomNumber());
				}
				if (StringUtil.isNotBlank(importData.getUserMeetingSeatNumber())) {
					user.setMeetingSeatNumber(importData.getUserMeetingSeatNumber());
				}

				userService.saveOrUpdate(user);
			} else {
				// 用户存在，更新用户信息
				if (StringUtil.isNotBlank(importData.getUserPhone()) &&
					!importData.getUserPhone().equals(user.getPhone())) {
					user.setPhone(importData.getUserPhone());
					userUpdated = true;
				}

				if (StringUtil.isNotBlank(importData.getUserEmployeeNumber()) &&
					!importData.getUserEmployeeNumber().equals(user.getEmployeeNumber())) {
					user.setEmployeeNumber(importData.getUserEmployeeNumber());
					userUpdated = true;
				}

				if (StringUtil.isNotBlank(importData.getUserRoomNumber()) &&
					!importData.getUserRoomNumber().equals(user.getRoomNumber())) {
					user.setRoomNumber(importData.getUserRoomNumber());
					userUpdated = true;
				}

				if (StringUtil.isNotBlank(importData.getUserMeetingSeatNumber()) &&
					!importData.getUserMeetingSeatNumber().equals(user.getMeetingSeatNumber())) {
					user.setMeetingSeatNumber(importData.getUserMeetingSeatNumber());
					userUpdated = true;
				}

				if (userUpdated) {
					userService.updateById(user);
				}
			}

			return user;
		} catch (Exception e) {
			errorMessages.add("第" + rowNum + "行：用户信息处理失败 - " + e.getMessage());
			return null;
		}
	}

	@Override
	public List<UserScheduleImportExcel> getImportTemplate() {
		// 返回模板数据，包含示例行
		List<UserScheduleImportExcel> templateList = new ArrayList<>();

		UserScheduleImportExcel example = new UserScheduleImportExcel();
		example.setUserRealName("张三");
		example.setUserEmployeeNumber("EMP001");
		example.setUserPhone("13800138000");
		example.setUserRoomNumber("A101");
		example.setUserMeetingSeatNumber("A-01");
		example.setScheduleContent("我的会议日程<p><strong>第一天（2024-07-29）</strong></p><ul><li>09:00-10:30 开幕式及主旨演讲</li><li>10:45-12:00 技术分享会</li><li>14:00-15:30 圆桌讨论</li><li>16:00-17:00 闭幕式</li></ul>");
		example.setDiningInfo("");
		example.setAccommodationInfo("");

		templateList.add(example);
		return templateList;
	}

	/**
	 * 生成唯一的用户名
	 *
	 * @param realName 真实姓名
	 * @return 唯一的用户名
	 */
	private String generateUniqueUsername(String realName) {
		// 使用拼音工具生成基础用户名
		String baseUsername = PinyinUtil.generateUsername(realName);

		// 检查用户名是否已存在，如果存在则添加数字后缀
		String username = baseUsername;
		int suffix = 1;

		while (isUsernameExists(username)) {
			username = baseUsername + suffix;
			suffix++;
		}

		return username;
	}

	/**
	 * 检查用户名是否已存在
	 *
	 * @param username 用户名
	 * @return 是否存在
	 */
	private boolean isUsernameExists(String username) {
		// 使用userByAccount方法查询用户
		User existingUser = userService.userByAccount("000000", username);
		return existingUser != null;
	}

}
